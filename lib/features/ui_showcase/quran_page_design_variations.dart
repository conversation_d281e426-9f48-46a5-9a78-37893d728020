import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter_svg/flutter_svg.dart';
import 'widgets/perfect_quran_page.dart';

/// Two innovative design variations for Quran page overlay controls
/// These designs showcase <PERSON><PERSON><PERSON>'s capability to create unique, non-Material interfaces

// ============================================================================
// DESIGN 1: ORBITAL MENU SYSTEM - Celestial-inspired floating controls
// ============================================================================

class OrbitalQuranPageDesign extends StatefulWidget {
  const OrbitalQuranPageDesign({Key? key}) : super(key: key);

  @override
  State<OrbitalQuranPageDesign> createState() => _OrbitalQuranPageDesignState();
}

class _OrbitalQuranPageDesignState extends State<OrbitalQuranPageDesign>
    with TickerProviderStateMixin {
  late AnimationController _orbitalController;
  late AnimationController _pulseController;
  late AnimationController _contextMenuController;
  late Animation<double> _orbitalAnimation;
  late Animation<double> _pulseAnimation;
  
  bool _menuExpanded = false;
  Offset? _contextMenuPosition;
  int? _selectedVerse;
  
  final List<OrbitalMenuItem> _orbitalItems = [
    OrbitalMenuItem(
      icon: Icons.bookmark_outline_rounded,
      label: 'Bookmark',
      color: Color(0xFF6B4DE6),
      angle: 0,
    ),
    OrbitalMenuItem(
      icon: Icons.auto_awesome_rounded,
      label: 'AI Insights',
      color: Color(0xFF4F7FFF),
      angle: math.pi / 3,
    ),
    OrbitalMenuItem(
      icon: Icons.translate_rounded,
      label: 'Translation',
      color: Color(0xFF00BFA5),
      angle: 2 * math.pi / 3,
    ),
    OrbitalMenuItem(
      icon: Icons.share_rounded,
      label: 'Share',
      color: Color(0xFFFF6B6B),
      angle: math.pi,
    ),
    OrbitalMenuItem(
      icon: Icons.library_books_rounded,
      label: 'Tafsir',
      color: Color(0xFFFFB800),
      angle: 4 * math.pi / 3,
    ),
    OrbitalMenuItem(
      icon: Icons.play_circle_outline_rounded,
      label: 'Audio',
      color: Color(0xFF00D9FF),
      angle: 5 * math.pi / 3,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _orbitalController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _contextMenuController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _orbitalAnimation = CurvedAnimation(
      parent: _orbitalController,
      curve: Curves.elasticOut,
    );
    
    _pulseAnimation = CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _orbitalController.dispose();
    _pulseController.dispose();
    _contextMenuController.dispose();
    super.dispose();
  }

  void _toggleOrbitalMenu() {
    HapticFeedback.lightImpact();
    setState(() {
      _menuExpanded = !_menuExpanded;
    });
    if (_menuExpanded) {
      _orbitalController.forward();
    } else {
      _orbitalController.reverse();
    }
  }

  void _showContextMenu(Offset position, int verseNumber) {
    HapticFeedback.mediumImpact();
    setState(() {
      _contextMenuPosition = position;
      _selectedVerse = verseNumber;
    });
    _contextMenuController.forward();
  }

  void _hideContextMenu() {
    _contextMenuController.reverse().then((_) {
      setState(() {
        _contextMenuPosition = null;
        _selectedVerse = null;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F4E6),
      body: Stack(
        children: [
          // Quran Page
          GestureDetector(
            onLongPressStart: (details) {
              _showContextMenu(details.globalPosition, 1); // Demo verse
            },
            onTap: () {
              if (_contextMenuPosition != null) {
                _hideContextMenu();
              }
            },
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFDF5),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.brown.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: PerfectQuranPage(
                  pageIndex: 0,
                  backgroundColor: const Color(0xFFFFFDF5),
                  textColor: Colors.black87,
                  bannerPath: 'assets/svg/surah_banner_modern.svg',
                ),
              ),
            ),
          ),
          
          // Orbital Menu System
          Positioned(
            bottom: 100,
            right: 30,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Orbital items
                ..._orbitalItems.map((item) {
                  return AnimatedBuilder(
                    animation: _orbitalAnimation,
                    builder: (context, child) {
                      final radius = 120.0 * _orbitalAnimation.value;
                      final x = radius * math.cos(item.angle);
                      final y = radius * math.sin(item.angle);
                      
                      return Transform.translate(
                        offset: Offset(x, y),
                        child: Transform.scale(
                          scale: _orbitalAnimation.value,
                          child: _buildOrbitalItem(item),
                        ),
                      );
                    },
                  );
                }).toList(),
                
                // Central control button
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            Color(0xFF6B4DE6),
                            Color(0xFF4F7FFF),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Color(0xFF6B4DE6).withOpacity(0.3),
                            blurRadius: 20 + (10 * _pulseAnimation.value),
                            spreadRadius: 5 * _pulseAnimation.value,
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _toggleOrbitalMenu,
                          borderRadius: BorderRadius.circular(35),
                          child: Center(
                            child: AnimatedRotation(
                              duration: const Duration(milliseconds: 300),
                              turns: _menuExpanded ? 0.125 : 0,
                              child: Icon(
                                _menuExpanded ? Icons.close : Icons.menu_book_rounded,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          
          // Context Menu (appears on long press)
          if (_contextMenuPosition != null)
            Positioned(
              top: _contextMenuPosition!.dy - 50,
              left: _contextMenuPosition!.dx - 150,
              child: AnimatedBuilder(
                animation: _contextMenuController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _contextMenuController.value,
                    child: Opacity(
                      opacity: _contextMenuController.value,
                      child: _buildCelestialContextMenu(),
                    ),
                  );
                },
              ),
            ),
          
          // Header
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                height: 60,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: () => Navigator.pop(context),
                    ),
                    Text(
                      'Orbital Design',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(width: 48),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrbitalItem(OrbitalMenuItem item) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: item.color.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            // Handle action
          },
          borderRadius: BorderRadius.circular(28),
          child: Center(
            child: Icon(
              item.icon,
              color: item.color,
              size: 26,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCelestialContextMenu() {
    return Container(
      width: 300,
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.95),
            Colors.white.withOpacity(0.85),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 30,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Verse preview
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.grey.withOpacity(0.2),
                    ),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                      style: TextStyle(
                        fontFamily: 'uthmanic2',
                        fontSize: 20,
                        height: 1.8,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Verse $_selectedVerse',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              // Action grid
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildContextAction(
                      icon: Icons.content_copy_rounded,
                      label: 'Copy',
                      color: Color(0xFF6B4DE6),
                    ),
                    _buildContextAction(
                      icon: Icons.auto_awesome_rounded,
                      label: 'AI',
                      color: Color(0xFF4F7FFF),
                    ),
                    _buildContextAction(
                      icon: Icons.bookmark_add_rounded,
                      label: 'Save',
                      color: Color(0xFF00BFA5),
                    ),
                    _buildContextAction(
                      icon: Icons.share_rounded,
                      label: 'Share',
                      color: Color(0xFFFF6B6B),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContextAction({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color.withOpacity(0.1),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                HapticFeedback.lightImpact();
                _hideContextMenu();
              },
              borderRadius: BorderRadius.circular(25),
              child: Center(
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }
}

// ============================================================================
// DESIGN 2: MINIMALIST GESTURE-BASED SYSTEM - Edge swipe controls
// ============================================================================

class MinimalistQuranPageDesign extends StatefulWidget {
  const MinimalistQuranPageDesign({Key? key}) : super(key: key);

  @override
  State<MinimalistQuranPageDesign> createState() => _MinimalistQuranPageDesignState();
}

class _MinimalistQuranPageDesignState extends State<MinimalistQuranPageDesign>
    with TickerProviderStateMixin {
  late AnimationController _edgeMenuController;
  late AnimationController _contextRippleController;
  late AnimationController _glowController;
  late Animation<double> _edgeMenuAnimation;
  late Animation<double> _contextRippleAnimation;
  
  bool _leftEdgeMenuVisible = false;
  bool _rightEdgeMenuVisible = false;
  Offset? _contextMenuPosition;
  int? _selectedVerse;
  
  // Edge indicators
  double _leftEdgeGlow = 0.0;
  double _rightEdgeGlow = 0.0;

  @override
  void initState() {
    super.initState();
    _edgeMenuController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _contextRippleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _edgeMenuAnimation = CurvedAnimation(
      parent: _edgeMenuController,
      curve: Curves.easeOutCubic,
    );
    
    _contextRippleAnimation = CurvedAnimation(
      parent: _contextRippleController,
      curve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    _edgeMenuController.dispose();
    _contextRippleController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  void _handleHorizontalDragUpdate(DragUpdateDetails details) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dragPosition = details.globalPosition.dx;
    
    // Left edge detection
    if (dragPosition < 30) {
      setState(() {
        _leftEdgeGlow = 1.0;
        _leftEdgeMenuVisible = true;
      });
      _edgeMenuController.forward();
    } else if (dragPosition > screenWidth - 30) {
      // Right edge detection
      setState(() {
        _rightEdgeGlow = 1.0;
        _rightEdgeMenuVisible = true;
      });
      _edgeMenuController.forward();
    }
  }

  void _handleHorizontalDragEnd(DragEndDetails details) {
    Future.delayed(const Duration(milliseconds: 100), () {
      setState(() {
        _leftEdgeGlow = 0.0;
        _rightEdgeGlow = 0.0;
      });
    });
  }

  void _showMinimalContextMenu(Offset position, int verseNumber) {
    HapticFeedback.selectionClick();
    setState(() {
      _contextMenuPosition = position;
      _selectedVerse = verseNumber;
    });
    _contextRippleController.forward(from: 0);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      body: GestureDetector(
        onHorizontalDragUpdate: _handleHorizontalDragUpdate,
        onHorizontalDragEnd: _handleHorizontalDragEnd,
        onTap: () {
          if (_leftEdgeMenuVisible || _rightEdgeMenuVisible) {
            setState(() {
              _leftEdgeMenuVisible = false;
              _rightEdgeMenuVisible = false;
            });
            _edgeMenuController.reverse();
          }
          if (_contextMenuPosition != null) {
            setState(() {
              _contextMenuPosition = null;
              _selectedVerse = null;
            });
          }
        },
        child: Stack(
          children: [
            // Quran Page with minimal frame
            GestureDetector(
              onLongPressStart: (details) {
                _showMinimalContextMenu(details.globalPosition, 1);
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 40),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: PerfectQuranPage(
                    pageIndex: 0,
                    backgroundColor: const Color(0xFFFFFFFF),
                    textColor: Colors.black87,
                    bannerPath: 'assets/svg/surah_banner_minimal.svg',
                  ),
                ),
              ),
            ),
            
            // Left edge glow indicator
            AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _leftEdgeGlow,
              child: Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 3,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        Color(0xFF2196F3),
                        Color(0xFF2196F3).withOpacity(0),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Right edge glow indicator
            AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _rightEdgeGlow,
              child: Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 3,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerRight,
                      end: Alignment.centerLeft,
                      colors: [
                        Color(0xFF4CAF50),
                        Color(0xFF4CAF50).withOpacity(0),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Left edge menu (Reading tools)
            AnimatedBuilder(
              animation: _edgeMenuAnimation,
              builder: (context, child) {
                return Positioned(
                  left: -200 + (200 * _edgeMenuAnimation.value),
                  top: 0,
                  bottom: 0,
                  child: _buildLeftEdgeMenu(),
                );
              },
            ),
            
            // Right edge menu (Study tools)
            AnimatedBuilder(
              animation: _edgeMenuAnimation,
              builder: (context, child) {
                return Positioned(
                  right: -200 + (200 * _edgeMenuAnimation.value),
                  top: 0,
                  bottom: 0,
                  child: _buildRightEdgeMenu(),
                );
              },
            ),
            
            // Minimal context menu (ripple effect)
            if (_contextMenuPosition != null)
              Positioned(
                left: _contextMenuPosition!.dx - 150,
                top: _contextMenuPosition!.dy - 100,
                child: _buildMinimalContextMenu(),
              ),
            
            // Minimal header
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SafeArea(
                child: Container(
                  height: 48,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back, size: 20),
                        onPressed: () => Navigator.pop(context),
                        color: Colors.grey[600],
                      ),
                      Text(
                        'Minimalist Design',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(width: 40),
                    ],
                  ),
                ),
              ),
            ),
            
            // Subtle bottom indicator
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Center(
                child: AnimatedBuilder(
                  animation: _glowController,
                  builder: (context, child) {
                    return Container(
                      width: 60,
                      height: 3,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.3 * _glowController.value),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeftEdgeMenu() {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(5, 0),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Reading',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  letterSpacing: 1,
                ),
              ),
            ),
            _buildMinimalMenuItem(
              icon: Icons.bookmark_outline,
              label: 'Bookmarks',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.history,
              label: 'History',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.search,
              label: 'Search',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.format_list_numbered,
              label: 'Jump to',
              onTap: () {},
            ),
            const Divider(height: 32),
            _buildMinimalMenuItem(
              icon: Icons.text_increase,
              label: 'Text size',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.brightness_6_outlined,
              label: 'Theme',
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRightEdgeMenu() {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(-5, 0),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Study',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  letterSpacing: 1,
                ),
              ),
            ),
            _buildMinimalMenuItem(
              icon: Icons.auto_awesome_outlined,
              label: 'AI Insights',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.translate,
              label: 'Translation',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.menu_book,
              label: 'Tafsir',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.play_circle_outline,
              label: 'Audio',
              onTap: () {},
            ),
            const Divider(height: 32),
            _buildMinimalMenuItem(
              icon: Icons.share_outlined,
              label: 'Share',
              onTap: () {},
            ),
            _buildMinimalMenuItem(
              icon: Icons.notes_outlined,
              label: 'Notes',
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMinimalMenuItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.selectionClick();
          onTap();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: Colors.grey[700],
              ),
              const SizedBox(width: 16),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMinimalContextMenu() {
    return AnimatedBuilder(
      animation: _contextRippleAnimation,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // Ripple effect
            Transform.scale(
              scale: _contextRippleAnimation.value * 2,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.grey[300]!.withOpacity(0.3 * (1 - _contextRippleAnimation.value)),
                ),
              ),
            ),
            // Menu content
            Transform.scale(
              scale: 0.9 + (0.1 * _contextRippleAnimation.value),
              child: Container(
                width: 300,
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildMinimalAction(Icons.content_copy, 'Copy'),
                    Container(width: 1, height: 40, color: Colors.grey[200]),
                    _buildMinimalAction(Icons.auto_awesome_outlined, 'AI'),
                    Container(width: 1, height: 40, color: Colors.grey[200]),
                    _buildMinimalAction(Icons.bookmark_add_outlined, 'Save'),
                    Container(width: 1, height: 40, color: Colors.grey[200]),
                    _buildMinimalAction(Icons.share_outlined, 'Share'),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMinimalAction(IconData icon, String label) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.selectionClick();
          setState(() {
            _contextMenuPosition = null;
            _selectedVerse = null;
          });
        },
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20,
                color: Colors.grey[700],
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Helper classes
class OrbitalMenuItem {
  final IconData icon;
  final String label;
  final Color color;
  final double angle;

  OrbitalMenuItem({
    required this.icon,
    required this.label,
    required this.color,
    required this.angle,
  });
}