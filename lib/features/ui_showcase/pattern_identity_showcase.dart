import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../core/widgets/pattern_identity.dart';

/// Showcase screen demonstrating the Pattern-based brand identity
class PatternIdentityShowcase extends StatefulWidget {
  const PatternIdentityShowcase({Key? key}) : super(key: key);

  @override
  State<PatternIdentityShowcase> createState() => _PatternIdentityShowcaseState();
}

class _PatternIdentityShowcaseState extends State<PatternIdentityShowcase> {
  int _selectedNavIndex = 0;
  bool _isDarkMode = false;
  String _selectedPattern = PatternAssets.pattern7;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _isDarkMode ? PatternTheme.darkTheme : PatternTheme.lightTheme,
      child: Scaffold(
        appBar: PatternAppBar(
          title: 'Pattern Identity System',
          backgroundColor: _isDarkMode ? PatternColors.midnight : null,
          patternAsset: _selectedPattern,
          actions: [
            IconButton(
              icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
              onPressed: () {
                setState(() {
                  _isDarkMode = !_isDarkMode;
                });
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Brand Introduction
              PatternCard(
                backgroundColor: _isDarkMode 
                    ? PatternColors.midnight.withOpacity(0.8)
                    : Colors.white,
                patterns: [PatternAssets.pattern7, PatternAssets.pattern9],
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quranic Insights Brand Identity',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: _isDarkMode ? PatternColors.gold : PatternColors.deepTeal,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'A sophisticated design system using multiple geometric patterns, '
                      'creating layered visual effects that honor Islamic artistic traditions '
                      'while maintaining modern UI/UX principles.',
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.5,
                        color: _isDarkMode ? Colors.white70 : Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Pattern Selector
              _buildSectionTitle('Select Pattern'),
              const SizedBox(height: 16),
              _buildPatternSelector(),
              
              const PatternDivider(patternAsset: PatternAssets.pattern6),
              
              // Color Palette
              _buildSectionTitle('Brand Colors'),
              const SizedBox(height: 16),
              _buildColorPalette(),
              
              const PatternDivider(patternAsset: PatternAssets.pattern4),
              
              // Loading Indicators
              _buildSectionTitle('Loading States'),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      PatternLoadingIndicator(
                        size: 40,
                        patternAsset: PatternAssets.pattern7,
                      ),
                      const SizedBox(height: 8),
                      Text('Pattern 7', style: _captionStyle),
                    ],
                  ),
                  Column(
                    children: [
                      PatternLoadingIndicator(
                        size: 60,
                        color: PatternColors.gold,
                        patternAsset: PatternAssets.pattern9,
                      ),
                      const SizedBox(height: 8),
                      Text('Pattern 9', style: _captionStyle),
                    ],
                  ),
                  Column(
                    children: [
                      PatternLoadingIndicator(
                        size: 80,
                        color: PatternColors.emerald,
                        patternAsset: PatternAssets.pattern10,
                      ),
                      const SizedBox(height: 8),
                      Text('Pattern 10', style: _captionStyle),
                    ],
                  ),
                ],
              ),
              
              const PatternDivider(patternAsset: PatternAssets.pattern8),
              
              // Pattern Overlays
              _buildSectionTitle('Pattern Overlay Effects'),
              const SizedBox(height: 16),
              _buildOverlayExamples(),
              
              const PatternDivider(patternAsset: PatternAssets.pattern5),
              
              // Cards Examples
              _buildSectionTitle('Card Components'),
              const SizedBox(height: 16),
              PatternCard(
                backgroundColor: _isDarkMode 
                    ? PatternColors.midnight.withOpacity(0.8)
                    : Colors.white,
                patterns: [PatternAssets.pattern5, PatternAssets.pattern8],
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Card tapped!')),
                  );
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        PatternIconButton(
                          icon: Icons.auto_awesome,
                          onPressed: () {},
                          iconColor: PatternColors.gold,
                          backgroundColor: PatternColors.gold.withOpacity(0.1),
                          patternAsset: PatternAssets.pattern10,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Interactive Card',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: _isDarkMode ? Colors.white : PatternColors.deepTeal,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Cards feature multiple pattern decorations with different colors '
                      'and positions, creating depth and visual interest.',
                      style: TextStyle(
                        color: _isDarkMode ? Colors.white70 : Colors.grey[600],
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Grid of feature cards
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.5,
                children: [
                  _buildFeatureCard(
                    icon: Icons.book,
                    title: 'Quran',
                    color: PatternColors.deepTeal,
                    pattern: PatternAssets.pattern4,
                  ),
                  _buildFeatureCard(
                    icon: Icons.psychology,
                    title: 'AI Insights',
                    color: PatternColors.azure,
                    pattern: PatternAssets.pattern5,
                  ),
                  _buildFeatureCard(
                    icon: Icons.bookmark,
                    title: 'Bookmarks',
                    color: PatternColors.emerald,
                    pattern: PatternAssets.pattern6,
                  ),
                  _buildFeatureCard(
                    icon: Icons.settings,
                    title: 'Settings',
                    color: PatternColors.amethyst,
                    pattern: PatternAssets.pattern7,
                  ),
                ],
              ),
              
              const PatternDivider(patternAsset: PatternAssets.pattern9),
              
              // Icon Buttons Section
              _buildSectionTitle('Pattern Icon Buttons'),
              const SizedBox(height: 16),
              Wrap(
                spacing: 16,
                runSpacing: 16,
                children: [
                  PatternIconButton(
                    icon: Icons.favorite,
                    onPressed: () {},
                    iconColor: PatternColors.coral,
                    backgroundColor: PatternColors.coral.withOpacity(0.1),
                    patternAsset: PatternAssets.pattern4,
                  ),
                  PatternIconButton(
                    icon: Icons.share,
                    onPressed: () {},
                    iconColor: PatternColors.azure,
                    backgroundColor: PatternColors.azure.withOpacity(0.1),
                    patternAsset: PatternAssets.pattern5,
                    size: 56,
                  ),
                  PatternIconButton(
                    icon: Icons.star,
                    onPressed: () {},
                    iconColor: PatternColors.gold,
                    backgroundColor: PatternColors.gold.withOpacity(0.1),
                    patternAsset: PatternAssets.pattern6,
                    size: 64,
                  ),
                ],
              ),
              
              const SizedBox(height: 80), // Space for FAB
            ],
          ),
        ),
        floatingActionButton: PatternFloatingActionButton(
          onPressed: () {
            // Show splash screen demo
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PatternSplashScreen(),
              ),
            );
          },
          icon: Icons.play_arrow,
          backgroundColor: _isDarkMode ? PatternColors.gold : null,
          patternAsset: _selectedPattern,
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: _isDarkMode ? PatternColors.gold : PatternColors.deepTeal,
      ),
    );
  }

  Widget _buildPatternSelector() {
    return Container(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: PatternAssets.allPatterns.length,
        itemBuilder: (context, index) {
          final pattern = PatternAssets.allPatterns[index];
          final isSelected = pattern == _selectedPattern;
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedPattern = pattern;
              });
            },
            child: Container(
              width: 80,
              height: 80,
              margin: const EdgeInsets.only(right: 12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? PatternColors.deepTeal.withOpacity(0.1)
                    : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected 
                      ? PatternColors.deepTeal
                      : Colors.grey[300]!,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: SvgPicture.asset(
                  pattern,
                  colorFilter: ColorFilter.mode(
                    isSelected 
                        ? PatternColors.deepTeal
                        : Colors.grey[600]!,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOverlayExamples() {
    return Column(
      children: [
        PatternOverlay(
          patternAsset: PatternAssets.pattern6,
          color: PatternColors.gold,
          opacity: 0.1,
          patternScale: 0.5,
          alignment: Alignment.topRight,
          child: Container(
            height: 150,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  PatternColors.deepTeal,
                  PatternColors.azure,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                'Pattern Overlay Effect',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildColorPalette() {
    final colors = [
      ('Deep Teal', PatternColors.deepTeal),
      ('Gold', PatternColors.gold),
      ('Cream', PatternColors.cream),
      ('Midnight', PatternColors.midnight),
      ('Azure', PatternColors.azure),
      ('Emerald', PatternColors.emerald),
      ('Coral', PatternColors.coral),
      ('Amethyst', PatternColors.amethyst),
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: colors.map((color) {
        return Column(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: color.$2,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: color.$2.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              color.$1,
              style: TextStyle(
                fontSize: 12,
                color: _isDarkMode ? Colors.white70 : Colors.grey[700],
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required Color color,
    required String pattern,
  }) {
    return PatternCard(
      backgroundColor: _isDarkMode 
          ? PatternColors.midnight.withOpacity(0.8)
          : Colors.white,
      patterns: [pattern],
      onTap: () {},
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          PatternIconButton(
            icon: icon,
            onPressed: () {},
            iconColor: color,
            backgroundColor: color.withOpacity(0.1),
            patternAsset: pattern,
            size: 48,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: _isDarkMode ? Colors.white : PatternColors.midnight,
            ),
          ),
        ],
      ),
    );
  }

  TextStyle get _captionStyle => TextStyle(
    fontSize: 14,
    color: _isDarkMode ? Colors.white70 : Colors.grey[600],
  );
}