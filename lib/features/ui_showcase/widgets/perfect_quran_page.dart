import 'package:flutter/material.dart';
import 'package:quran_library/quran.dart';
import 'package:get/get.dart';
import '../../../core/utils/constants/extensions/modern_menu_extension.dart';

/// Perfect Quran page implementation based on alquranalkareem approach
class PerfectQuranPage extends StatefulWidget {
  final int pageIndex;
  final Color backgroundColor;
  final Color textColor;
  final String? bannerPath;
  
  const PerfectQuranPage({
    Key? key,
    required this.pageIndex,
    required this.backgroundColor,
    required this.textColor,
    this.bannerPath,
  }) : super(key: key);
  
  @override
  State<PerfectQuranPage> createState() => _PerfectQuranPageState();
}

class _PerfectQuranPageState extends State<PerfectQuranPage> {
  
  @override
  Widget build(BuildContext context) {
    // Use GetBuilder with clearSelection id like alquranalkareem does
    return GetBuilder<QuranCtrl>(
      id: 'clearSelection',
      builder: (quranCtrl) => QuranLibraryScreen(
        withPageView: false, // We handle single page display
        pageIndex: widget.pageIndex,
        useDefaultAppBar: false, // We have our own header
        isDark: false,
        languageCode: 'ar',
        backgroundColor: widget.backgroundColor,
        textColor: widget.textColor,
        ayahSelectedBackgroundColor: const Color(0xFF4CAF50).withOpacity(0.3),
        bookmarksColor: const Color(0xffCD9974).withOpacity(0.4),
        isFontsLocal: true, // Use local fonts like alquranalkareem
        fontsName: 'page${widget.pageIndex + 1}', // Font naming pattern from alquranalkareem
        ayahIconColor: const Color(0xFF1B5E20),
        bannerStyle: BannerStyle(
          isImage: false,
          bannerSvgHeight: 140,
          bannerSvgPath: widget.bannerPath ?? 'assets/svg/surah_banner_custom.svg',
        ),
        basmalaStyle: BasmalaStyle(
          basmalaColor: widget.textColor.withOpacity(0.8),
          basmalaHeight: 140,
        ),
        surahNameStyle: SurahNameStyle(
          surahNameColor: const Color(0xFF1B5E20),
          surahNameHeight: 100,
        ),
        onPagePress: () {
          // Clear selection when clicking on empty space
          QuranCtrl.instance.clearSelection();
          QuranCtrl.instance.update(['clearSelection']);
        },
        onAyahLongPress: (details, ayah) {
          print('Before toggle - Selected ayahs: ${QuranCtrl.instance.selectedAyahsByUnequeNumber}');
          
          // Show modern menu on long press
          context.showModernAyahMenu(
            surahNum: ayah.surahNumber ?? 0,
            ayahNum: ayah.ayahNumber,
            ayahText: ayah.text,
            pageIndex: widget.pageIndex,
            ayahTextNormal: ayah.text,
            ayahUQNum: ayah.ayahUQNumber,
            surahName: ayah.englishName ?? '',
            details: details,
          );
          
          // Toggle selection after showing menu (like alquranalkareem does)
          QuranCtrl.instance.toggleAyahSelection(ayah.ayahUQNumber);
          
          // Manually trigger update with the clearSelection ID
          QuranCtrl.instance.update(['clearSelection']);
          
          print('After toggle - Selected ayahs: ${QuranCtrl.instance.selectedAyahsByUnequeNumber}');
          print('Ayah unique number: ${ayah.ayahUQNumber}');
        },
      ),
    );
  }
}