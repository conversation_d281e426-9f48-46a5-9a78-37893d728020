import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:math' as math;
import 'dart:ui';
import 'dart:async';
import 'package:get/get.dart';
import 'package:quran_library/quran.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'widgets/web_font_loader.dart';
import 'widgets/web_quran_page_wrapper.dart';
import 'widgets/perfect_quran_page.dart';
import 'widgets/surah_selection_drawer.dart';

/// An authentic Quran page viewer that showcases proper Quranic design
/// Demonstrates Flutter's capability to create beautiful Islamic interfaces
class AuthenticQuranPageView extends StatefulWidget {
  const AuthenticQuranPageView({Key? key}) : super(key: key);

  @override
  State<AuthenticQuranPageView> createState() => _AuthenticQuranPageViewState();
}

class _AuthenticQuranPageViewState extends State<AuthenticQuranPageView>
    with TickerProviderStateMixin {
  late AnimationController _pageTransitionController;
  late AnimationController _highlightController;
  late AnimationController _controlsAnimationController;
  late Animation<double> _pageAnimation;
  late Animation<double> _highlightAnimation;
  late Animation<double> _controlsAnimation;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  
  int _currentPage = 0; // Page 1 in the app (0-indexed)
  int? _selectedVerse;
  late Future<void> _initFuture;
  String _currentBannerStyle = 'assets/svg/surah_banner_custom.svg';
  String _currentSurahName = 'سورة الفاتحة';
  bool _showControls = true;
  Timer? _hideControlsTimer;
  PageController? _pageController;
  double _baseScaleFactor = 1.0;
  double _scaleFactor = 1.0;

  @override
  void initState() {
    super.initState();
    
    _pageTransitionController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pageAnimation = CurvedAnimation(
      parent: _pageTransitionController,
      curve: Curves.easeInOutCubic,
    );
    
    _highlightAnimation = CurvedAnimation(
      parent: _highlightController,
      curve: Curves.easeOut,
    );
    
    _controlsAnimation = CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    );
    
    _pageController = PageController(initialPage: _currentPage);
    _controlsAnimationController.forward();
    
    // Start timer to hide controls
    _startHideControlsTimer();
    
    // Initialize QuranLibrary
    _initFuture = _initializeQuran();
  }
  
  Future<void> _initializeQuran() async {
    try {
      await QuranLibrary().init();
      
      // Make sure Quran data is loaded with codeV2 for authentic fonts
      final quranCtrl = QuranCtrl.instance;
      
      // First load regular data to get line breaks (this populates staticPages)
      await quranCtrl.loadQuran();
      
      // Then load fonts data which includes codeV2
      await quranCtrl.loadFontsQuran();
      
      // Configure QuranLibrary like alquranalkareem does
      QuranLibrary().setFontsSelected = 1; // Enable font mode
      
      // Set fonts selected to 1 to use QuranFontsPage instead of QuranLinePage
      QuranCtrl.instance.state.fontsSelected.value = 1;
      
      // For web, we'll use local fonts instead of downloaded ones
      if (kIsWeb) {
        // Don't try to download fonts on web
        QuranCtrl.instance.state.isDownloadedV2Fonts.value = true; // Set to true to use codeV2
        
        // Load the font for the current page
        await WebFontLoader.loadPageFont(_currentPage + 1);
      } else {
        // For mobile platforms, check if fonts are downloaded
        // For now, we'll assume they're local like alquranalkareem
        QuranCtrl.instance.state.isDownloadedV2Fonts.value = true;
      }
      
      // Update current surah name
      _updateCurrentSurahName();
    } catch (e) {
      print('Error initializing Quran Library: $e');
    }
  }
  
  void _updateCurrentSurahName() {
    final quranCtrl = QuranCtrl.instance;
    if (quranCtrl.state.surahs.isNotEmpty) {
      // Find which surah the current page belongs to
      for (var surah in quranCtrl.state.surahs) {
        if (surah.ayahs.isNotEmpty) {
          final firstPage = surah.ayahs.first.page - 1;
          final lastPage = surah.ayahs.last.page - 1;
          if (_currentPage >= firstPage && _currentPage <= lastPage) {
            setState(() {
              _currentSurahName = surah.arabicName;
            });
            break;
          }
        }
      }
    }
  }

  @override
  void dispose() {
    _pageTransitionController.dispose();
    _highlightController.dispose();
    _controlsAnimationController.dispose();
    _hideControlsTimer?.cancel();
    _pageController?.dispose();
    super.dispose();
  }
  
  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }
  
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _controlsAnimationController.forward();
      _startHideControlsTimer();
    } else {
      _controlsAnimationController.reverse();
      _hideControlsTimer?.cancel();
    }
  }

  void _selectVerse(int verseNumber) {
    HapticFeedback.lightImpact();
    setState(() {
      _selectedVerse = verseNumber;
    });
    _highlightController.forward(from: 0);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xFFF8F4E6), // Traditional paper color
      endDrawer: SurahSelectionDrawer(
        currentPage: _currentPage,
        onSurahSelected: (int pageIndex, String surahName) {
          setState(() {
            _currentPage = pageIndex;
            _currentSurahName = surahName;
          });
          _pageController?.jumpToPage(pageIndex);
          // Load font for new page on web
          if (kIsWeb) {
            WebFontLoader.loadPageFont(pageIndex + 1);
          }
        },
      ),
      body: FutureBuilder<void>(
        future: _initFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return GestureDetector(
              onTap: _toggleControls,
              onScaleStart: (details) {
                _baseScaleFactor = _scaleFactor;
              },
              onScaleUpdate: (details) {
                setState(() {
                  _scaleFactor = _baseScaleFactor * details.scale;
                  _scaleFactor = _scaleFactor.clamp(0.8, 2.0);
                });
              },
              child: Stack(
                children: [
                  // Main Quran PageView
                  PageView.builder(
                    controller: _pageController,
                    itemCount: 604,
                    onPageChanged: (int page) async {
                      setState(() {
                        _currentPage = page;
                      });
                      _updateCurrentSurahName();
                      
                      // Load font for new page on web
                      if (kIsWeb) {
                        await WebFontLoader.loadPageFont(page + 1);
                      } else {
                        await QuranCtrl.instance.prepareFonts(page, isFontsLocal: true);
                      }
                      
                      _pageTransitionController.forward(from: 0);
                    },
                    itemBuilder: (context, index) {
                      return Transform.scale(
                        scale: _scaleFactor,
                        child: Container(
                          margin: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFDF5), // Cream paper color
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.brown.withOpacity(0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: PerfectQuranPage(
                              pageIndex: index,
                              backgroundColor: const Color(0xFFFFFDF5),
                              textColor: Colors.black87,
                              bannerPath: _currentBannerStyle,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  
                  // Animated Header
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: AnimatedBuilder(
                      animation: _controlsAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, -60 * (1 - _controlsAnimation.value)),
                          child: Opacity(
                            opacity: _controlsAnimation.value,
                            child: SafeArea(
                              child: _buildHeader(),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  
                  // Animated Bottom Controls
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: AnimatedBuilder(
                      animation: _controlsAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, 100 * (1 - _controlsAnimation.value)),
                          child: Opacity(
                            opacity: _controlsAnimation.value,
                            child: _buildBottomControls(),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF1B5E20),
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF1B5E20)),
            onPressed: () => Navigator.of(context).pop(),
          ),
          // Title
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _currentSurahName,
                style: TextStyle(
                  fontFamily: 'uthmanic2',
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF1B5E20),
                ),
              ),
              Text(
                'Page ${_currentPage + 1}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          // Surah Selection Menu
          IconButton(
            icon: const Icon(Icons.menu, color: Color(0xFF1B5E20)),
            onPressed: () {
              _scaffoldKey.currentState?.openEndDrawer();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.4),
            Colors.black.withOpacity(0.6),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Control buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Bookmark button
                _buildControlButton(
                  icon: Icons.bookmark_border,
                  onTap: () {
                    // Add bookmark functionality
                    HapticFeedback.lightImpact();
                  },
                ),
                // Jump to page button
                _buildControlButton(
                  icon: Icons.format_list_numbered,
                  onTap: () {
                    _showJumpToPageDialog();
                  },
                ),
                // Translation toggle
                _buildControlButton(
                  icon: Icons.translate,
                  onTap: () {
                    // Add translation functionality
                    HapticFeedback.lightImpact();
                  },
                ),
                // Share button
                _buildControlButton(
                  icon: Icons.share,
                  onTap: () {
                    // Add share functionality
                    HapticFeedback.lightImpact();
                  },
                ),
                // Settings button
                _buildControlButton(
                  icon: Icons.settings,
                  onTap: () {
                    // Add settings functionality
                    HapticFeedback.lightImpact();
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // Page progress bar
          Container(
            height: 3,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: (_currentPage + 1) / 604,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50),
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4CAF50).withOpacity(0.5),
                      blurRadius: 4,
                      offset: const Offset(0, 0),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
  
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          onTap();
          _startHideControlsTimer();
        },
        borderRadius: BorderRadius.circular(30),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: const Color(0xFF1B5E20),
            size: 24,
          ),
        ),
      ),
    );
  }

  String _convertToArabicNumber(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      return arabicNumbers[int.parse(digit)];
    }).join();
  }
  
  void _showJumpToPageDialog() {
    final TextEditingController pageController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'الذهاب إلى صفحة',
            style: TextStyle(
              fontFamily: 'uthmanic2',
              fontSize: 20,
            ),
            textAlign: TextAlign.center,
          ),
          content: TextField(
            controller: pageController,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            decoration: InputDecoration(
              hintText: '1 - 604',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Color(0xFF1B5E20),
                  width: 2,
                ),
              ),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            TextButton(
              onPressed: () {
                final pageNumber = int.tryParse(pageController.text);
                if (pageNumber != null && pageNumber >= 1 && pageNumber <= 604) {
                  Navigator.pop(context);
                  _pageController?.jumpToPage(pageNumber - 1);
                }
              },
              child: const Text(
                'انتقال',
                style: TextStyle(
                  color: Color(0xFF1B5E20),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

}

