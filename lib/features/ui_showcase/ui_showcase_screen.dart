import 'package:flutter/material.dart';
import 'custom_verse_card.dart';
import 'glassmorphic_ai_insight_card.dart';
import 'custom_quran_page_viewer.dart';
import 'animated_theme_selector.dart';
import 'custom_bottom_navigation.dart';
import 'authentic_quran_page_view.dart';
import 'quran_page_design_variations.dart';
import 'pattern_identity_showcase.dart';
import 'surah_banner_showcase.dart';

/// A showcase screen demonstrating advanced Flutter UI capabilities
/// This proves Flutter can create ANY design - not limited to Material
class UIShowcaseScreen extends StatefulWidget {
  const UIShowcaseScreen({Key? key}) : super(key: key);

  @override
  State<UIShowcaseScreen> createState() => _UIShowcaseScreenState();
}

class _UIShowcaseScreenState extends State<UIShowcaseScreen> {
  int _selectedThemeIndex = 0;
  bool _isBookmarked = false;
  int _selectedNavIndex = 0;
  bool _showQuranPage = false;
  
  final List<ThemeOption> _themes = [
    ThemeOption(
      name: 'Dawn',
      primaryColor: Color(0xFF6B4DE6),
      secondaryColor: Color(0xFF4F7FFF),
      backgroundColor: Color(0xFFF5F1E8),
      icon: Icons.wb_sunny_rounded,
      iconColor: Color(0xFFFFB800),
    ),
    ThemeOption(
      name: 'Night',
      primaryColor: Color(0xFF1E3A5F),
      secondaryColor: Color(0xFF0D2137),
      backgroundColor: Color(0xFF0A1628),
      icon: Icons.nights_stay_rounded,
      iconColor: Color(0xFFE8C547),
    ),
    ThemeOption(
      name: 'Nature',
      primaryColor: Color(0xFF2D6A4F),
      secondaryColor: Color(0xFF40916C),
      backgroundColor: Color(0xFFD8F3DC),
      icon: Icons.eco_rounded,
      iconColor: Color(0xFF52B788),
    ),
    ThemeOption(
      name: 'Ocean',
      primaryColor: Color(0xFF006BA6),
      secondaryColor: Color(0xFF0496FF),
      backgroundColor: Color(0xFFE0F4FF),
      icon: Icons.water_rounded,
      iconColor: Color(0xFF0077B6),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final currentTheme = _themes[_selectedThemeIndex];
    
    // Show authentic Quran page when Quran tab is selected
    if (_showQuranPage) {
      return AuthenticQuranPageView();
    }
    
    return Scaffold(
      backgroundColor: currentTheme.backgroundColor,
      body: Stack(
        children: [
          CustomScrollView(
        slivers: [
          // Custom App Bar with no Material restrictions
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      currentTheme.primaryColor,
                      currentTheme.secondaryColor,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Animated background pattern
                    CustomPaint(
                      size: Size.infinite,
                      painter: _AnimatedPatternPainter(
                        primaryColor: currentTheme.primaryColor,
                        secondaryColor: currentTheme.secondaryColor,
                      ),
                    ),
                    // Title
                    Positioned(
                      bottom: 40,
                      left: 20,
                      right: 20,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Flutter UI Showcase',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Beyond Material Design',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 18,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Content
          SliverPadding(
            padding: const EdgeInsets.only(bottom: 100),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // Theme Selector
                _buildSectionTitle('Animated Theme Selector'),
                AnimatedThemeSelector(
                  themes: _themes,
                  selectedIndex: _selectedThemeIndex,
                  onThemeSelected: (index) {
                    setState(() => _selectedThemeIndex = index);
                  },
                ),
                
                SizedBox(height: 40),
                
                // Custom Verse Card
                _buildSectionTitle('Custom Verse Card'),
                _buildDescription('Neumorphic design with custom painters and animations'),
                CustomVerseCard(
                  arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                  translation: 'In the name of Allah, the Most Gracious, the Most Merciful',
                  surahName: 'Al-Fatihah',
                  ayahNumber: 1,
                  isBookmarked: _isBookmarked,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Verse tapped!'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  onBookmark: () {
                    setState(() => _isBookmarked = !_isBookmarked);
                  },
                  onShare: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Share functionality'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
                
                SizedBox(height: 40),
                
                // Glassmorphic AI Card
                _buildSectionTitle('Glassmorphic AI Insight'),
                _buildDescription('Advanced blur effects, gradients, and animations'),
                GlassmorphicAIInsightCard(
                  title: 'The Opening Chapter',
                  insight: 'Al-Fatihah is known as "The Opening" and is the first chapter of the Quran. '
                      'It encapsulates the essence of the entire Quran and Islamic faith. '
                      'This chapter establishes the relationship between humanity and Allah, '
                      'emphasizing His mercy, sovereignty, and guidance. It serves as a prayer '
                      'for guidance and is recited in every unit of the Muslim prayer.',
                  concepts: ['Mercy', 'Guidance', 'Worship', 'Sovereignty'],
                  primaryColor: currentTheme.primaryColor,
                  secondaryColor: currentTheme.secondaryColor,
                ),
                
                SizedBox(height: 40),
                
                // Custom Quran Page Viewer
                _buildSectionTitle('Interactive Quran Page'),
                _buildDescription('Custom gestures, verse highlighting, and zoom controls'),
                Container(
                  height: 500,
                  margin: EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: currentTheme.primaryColor.withOpacity(0.1),
                        blurRadius: 20,
                        offset: Offset(0, 10),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: CustomQuranPageViewer(
                      pageImagePath: '', // No image needed for demo
                      verseRegions: [
                        VerseRegion(
                          ayahNumber: 1,
                          points: [
                            Offset(50, 100),
                            Offset(350, 100),
                            Offset(350, 150),
                            Offset(50, 150),
                          ],
                        ),
                        VerseRegion(
                          ayahNumber: 2,
                          points: [
                            Offset(50, 160),
                            Offset(350, 160),
                            Offset(350, 210),
                            Offset(50, 210),
                          ],
                        ),
                      ],
                      onVerseSelected: (ayah) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Selected Ayah $ayah'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      onVerseLongPress: (ayah) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Long pressed Ayah $ayah'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                
                SizedBox(height: 40),
                
                // Authentic Quran Page
                _buildSectionTitle('Authentic Quran Page'),
                _buildDescription('Traditional Islamic design with proper Arabic typography'),
                Container(
                  height: 600,
                  margin: EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: currentTheme.primaryColor.withOpacity(0.1),
                        blurRadius: 20,
                        offset: Offset(0, 10),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: AuthenticQuranPageView(),
                  ),
                ),
                
                SizedBox(height: 40),
                
                // Quran Page Design Variations
                _buildSectionTitle('Innovative Quran Page Designs'),
                _buildDescription('Non-Material overlay controls and context menus'),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      // Orbital Design Button
                      Container(
                        width: double.infinity,
                        height: 120,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0xFF6B4DE6), Color(0xFF4F7FFF)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Color(0xFF6B4DE6).withOpacity(0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => OrbitalQuranPageDesign(),
                                ),
                              );
                            },
                            borderRadius: BorderRadius.circular(20),
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.blur_circular_rounded,
                                    color: Colors.white,
                                    size: 40,
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Orbital Menu System',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'Celestial-inspired floating controls',
                                          style: TextStyle(
                                            color: Colors.white.withOpacity(0.8),
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.white.withOpacity(0.6),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Minimalist Design Button
                      Container(
                        width: double.infinity,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => MinimalistQuranPageDesign(),
                                ),
                              );
                            },
                            borderRadius: BorderRadius.circular(20),
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.gesture_rounded,
                                    color: Colors.grey[800],
                                    size: 40,
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Minimalist Gesture System',
                                          style: TextStyle(
                                            color: Colors.grey[800],
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'Edge swipe controls with subtle interactions',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.grey[400],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                SizedBox(height: 40),
                
                // Islamic Mosaic Brand Identity
                _buildSectionTitle('Brand Identity System'),
                _buildDescription('Complete design system using Islamic mosaic patterns'),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Container(
                    width: double.infinity,
                    height: 140,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFF006B6B),
                          Color(0xFF4A90E2),
                          Color(0xFFD4AF37),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0xFF006B6B).withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PatternIdentityShowcase(),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(20),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Row(
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.palette_rounded,
                                  color: Colors.white,
                                  size: 32,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'Pattern Identity System',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 22,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Multi-pattern overlays, animated components, branded UI',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.9),
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                
                SizedBox(height: 40),
                
                // Surah Banner Variations
                _buildSectionTitle('Surah Banner Designs'),
                _buildDescription('Pattern-based Surah headers with multiple styles'),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Container(
                    width: double.infinity,
                    height: 120,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFF2ECC71),
                          Color(0xFF27AE60),
                          Color(0xFF229954),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0xFF27AE60).withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SurahBannerShowcase(),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(20),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Row(
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.view_carousel_rounded,
                                  color: Colors.white,
                                  size: 32,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'Surah Banner Variations',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      '5 unique designs with pattern integration',
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.9),
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                
                SizedBox(height: 40),
                
                // More Examples Section
                _buildSectionTitle('What Else is Possible?'),
                _buildFeatureList(),
              ]),
            ),
          ),
        ],
      ),
          // Custom Bottom Navigation
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: CustomBottomNavigation(
              items: [
                NavigationItem(icon: Icons.home_rounded, label: 'Home'),
                NavigationItem(icon: Icons.auto_awesome_rounded, label: 'AI'),
                NavigationItem(icon: Icons.book_rounded, label: 'Quran'),
                NavigationItem(icon: Icons.settings_rounded, label: 'Settings'),
              ],
              selectedIndex: _selectedNavIndex,
              onItemSelected: (index) {
                setState(() {
                  _selectedNavIndex = index;
                  // Show Quran page when Quran tab (index 2) is selected
                  _showQuranPage = index == 2;
                });
              },
              backgroundColor: currentTheme.primaryColor.withOpacity(0.95),
              selectedColor: currentTheme.backgroundColor,
              unselectedColor: currentTheme.backgroundColor.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: _themes[_selectedThemeIndex].primaryColor,
        ),
      ),
    );
  }

  Widget _buildDescription(String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Text(
        description,
        style: TextStyle(
          fontSize: 16,
          color: Colors.grey[600],
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildFeatureList() {
    final features = [
      '🎨 Particle animations and physics simulations',
      '🌊 Liquid animations and morphing effects',
      '✨ Custom shaders and GPU effects',
      '🎭 3D transformations and perspectives',
      '🎪 Complex gesture recognizers',
      '🎯 Pixel-perfect custom rendering',
      '🌈 Advanced color mixing and gradients',
      '📐 Bezier curves and path animations',
      '🔮 Neumorphic and glassmorphic designs',
      '⚡ High-performance custom scrolling',
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: features.map((feature) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Text(
                feature.substring(0, 2),
                style: TextStyle(fontSize: 24),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  feature.substring(2),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),
        )).toList(),
      ),
    );
  }
}

/// Animated pattern painter for the app bar
class _AnimatedPatternPainter extends CustomPainter {
  final Color primaryColor;
  final Color secondaryColor;

  _AnimatedPatternPainter({
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Draw flowing lines
    for (int i = 0; i < 5; i++) {
      final path = Path();
      final startY = size.height * (0.2 + i * 0.15);
      
      path.moveTo(0, startY);
      
      for (double x = 0; x <= size.width; x += 10) {
        final y = startY + 20 * sin(x * 0.02 + i);
        path.lineTo(x, y);
      }
      
      canvas.drawPath(path, paint);
    }
  }

  double sin(double x) => (x - x * x * x / 6 + x * x * x * x * x / 120);

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}