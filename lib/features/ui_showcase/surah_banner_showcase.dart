import 'package:flutter/material.dart';
import '../../core/widgets/pattern_surah_banners.dart';
import '../../core/widgets/pattern_surah_banner_frames.dart';
import '../../core/widgets/pattern_identity.dart';

/// Showcase screen for pattern-based Surah banner variations
class SurahBannerShowcase extends StatefulWidget {
  const SurahBannerShowcase({Key? key}) : super(key: key);

  @override
  State<SurahBannerShowcase> createState() => _SurahBannerShowcaseState();
}

class _SurahBannerShowcaseState extends State<SurahBannerShowcase> {
  final PageController _pageController = PageController(viewportFraction: 0.9);
  int _currentPage = 0;
  
  // Sample Surah data
  final List<Map<String, dynamic>> _sampleSurahs = [
    {
      'name': 'Al-Fatihah',
      'nameArabic': 'سُورَةُ الفَاتِحَة',
      'number': 1,
      'ayahCount': 7,
      'revelationType': 'Meccan',
    },
    {
      'name': '<PERSON>-<PERSON><PERSON><PERSON>',
      'nameArabic': 'سُورَةُ البَقَرَة',
      'number': 2,
      'ayahCount': 286,
      'revelationType': 'Medinan',
    },
    {
      'name': 'Al-Kahf',
      'nameArabic': 'سُورَةُ الكَهْف',
      'number': 18,
      'ayahCount': 110,
      'revelationType': 'Meccan',
    },
    {
      'name': 'Ya-Sin',
      'nameArabic': 'سُورَةُ يس',
      'number': 36,
      'ayahCount': 83,
      'revelationType': 'Meccan',
    },
    {
      'name': 'Al-Mulk',
      'nameArabic': 'سُورَةُ المُلْك',
      'number': 67,
      'ayahCount': 30,
      'revelationType': 'Meccan',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final currentSurah = _sampleSurahs[_currentPage % _sampleSurahs.length];
    
    return Scaffold(
      backgroundColor: PatternColors.cream,
      appBar: PatternAppBar(
        title: 'Surah Banner Designs',
        patternAsset: PatternAssets.pattern6,
      ),
      body: Column(
        children: [
          // Surah selector
          Container(
            height: 80,
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _sampleSurahs.length,
              itemBuilder: (context, index) {
                final surah = _sampleSurahs[index];
                final isSelected = index == _currentPage;
                
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _currentPage = index;
                    });
                    _pageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? PatternColors.deepTeal : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: isSelected 
                              ? PatternColors.deepTeal.withOpacity(0.3)
                              : Colors.grey.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        surah['number'].toString(),
                        style: TextStyle(
                          color: isSelected ? Colors.white : PatternColors.deepTeal,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Banner designs carousel
          Expanded(
            child: ListView(
              padding: const EdgeInsets.only(bottom: 20),
              children: [
                // Design 1: Geometric Pattern Banner
                _buildBannerSection(
                  title: 'Geometric Pattern Banner',
                  description: 'Layered patterns with gradient background',
                  child: GeometricPatternBanner(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Design 2: Minimalist Pattern Banner
                _buildBannerSection(
                  title: 'Minimalist Pattern Banner',
                  description: 'Clean design with subtle pattern accents',
                  child: MinimalistPatternBanner(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Design 3: Ornate Pattern Banner
                _buildBannerSection(
                  title: 'Ornate Pattern Banner',
                  description: 'Rich design with animated layered patterns',
                  child: OrnatePatternBanner(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Design 4: Islamic Tile Banner
                _buildBannerSection(
                  title: 'Islamic Tile Banner',
                  description: 'Inspired by traditional mosque tile patterns',
                  child: IslamicTileBanner(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Design 5: Modern Glass Banner
                _buildBannerSection(
                  title: 'Modern Glass Banner',
                  description: 'Glassmorphic design with pattern accents',
                  child: ModernGlassBanner(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Banner Frame Designs Section
                const SizedBox(height: 40),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    'Banner Frame Designs',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: PatternColors.deepTeal,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    'Frames similar to existing SVG banners with pattern integration',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Frame Design 1: Custom Pattern Banner (Ornate)
                _buildBannerSection(
                  title: 'Custom Pattern Frame',
                  description: 'Ornate frame style with corner patterns',
                  child: CustomPatternBanner(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Frame Design 2: Minimal Pattern Frame
                _buildBannerSection(
                  title: 'Minimal Pattern Frame',
                  description: 'Clean frame with subtle pattern accent',
                  child: MinimalPatternBannerFrame(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Frame Design 3: Modern Pattern Frame
                _buildBannerSection(
                  title: 'Modern Pattern Frame',
                  description: 'Contemporary geometric frame design',
                  child: ModernPatternBannerFrame(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Frame Design 4: Traditional Pattern Banner
                _buildBannerSection(
                  title: 'Traditional Pattern Frame',
                  description: 'Classic Islamic frame with symmetrical patterns',
                  child: TraditionalPatternBanner(
                    surahName: currentSurah['name'],
                    surahNameArabic: currentSurah['nameArabic'],
                    surahNumber: currentSurah['number'],
                    ayahCount: currentSurah['ayahCount'],
                    revelationType: currentSurah['revelationType'],
                  ),
                ),
                
                // Color variations section
                const SizedBox(height: 40),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    'Color Variations',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: PatternColors.deepTeal,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Geometric banner with different colors
                GeometricPatternBanner(
                  surahName: currentSurah['name'],
                  surahNameArabic: currentSurah['nameArabic'],
                  surahNumber: currentSurah['number'],
                  ayahCount: currentSurah['ayahCount'],
                  revelationType: currentSurah['revelationType'],
                  primaryColor: PatternColors.emerald,
                  secondaryColor: PatternColors.azure,
                ),
                const SizedBox(height: 16),
                
                GeometricPatternBanner(
                  surahName: currentSurah['name'],
                  surahNameArabic: currentSurah['nameArabic'],
                  surahNumber: currentSurah['number'],
                  ayahCount: currentSurah['ayahCount'],
                  revelationType: currentSurah['revelationType'],
                  primaryColor: PatternColors.amethyst,
                  secondaryColor: PatternColors.coral,
                ),
                const SizedBox(height: 16),
                
                // Minimalist banner with different accent colors
                MinimalistPatternBanner(
                  surahName: currentSurah['name'],
                  surahNameArabic: currentSurah['nameArabic'],
                  surahNumber: currentSurah['number'],
                  ayahCount: currentSurah['ayahCount'],
                  revelationType: currentSurah['revelationType'],
                  accentColor: PatternColors.gold,
                ),
                const SizedBox(height: 16),
                
                MinimalistPatternBanner(
                  surahName: currentSurah['name'],
                  surahNameArabic: currentSurah['nameArabic'],
                  surahNumber: currentSurah['number'],
                  ayahCount: currentSurah['ayahCount'],
                  revelationType: currentSurah['revelationType'],
                  accentColor: PatternColors.coral,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBannerSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: PatternColors.midnight,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        child,
        const SizedBox(height: 24),
      ],
    );
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}