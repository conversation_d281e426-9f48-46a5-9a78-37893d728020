import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// Pattern-based Identity System for Quranic Insights App
/// Uses multiple geometric patterns from the pattern folder for various UI elements

// Brand Colors inspired by traditional Islamic art
class PatternColors {
  static const Color deepTeal = Color(0xFF006B6B);
  static const Color gold = Color(0xFFD4AF37);
  static const Color cream = Color(0xFFFFF8E7);
  static const Color midnight = Color(0xFF1A2332);
  static const Color azure = Color(0xFF4A90E2);
  static const Color emerald = Color(0xFF2ECC71);
  static const Color coral = Color(0xFFE74C3C);
  static const Color amethyst = Color(0xFF9B59B6);
}

// Pattern paths
class PatternAssets {
  static const String pattern4 = 'assets/svg/pattern/Asset_4.svg';
  static const String pattern5 = 'assets/svg/pattern/Asset_5.svg';
  static const String pattern6 = 'assets/svg/pattern/Asset_6.svg';
  static const String pattern7 = 'assets/svg/pattern/Asset_7.svg';
  static const String pattern8 = 'assets/svg/pattern/Asset_8.svg';
  static const String pattern9 = 'assets/svg/pattern/Asset_9.svg';
  static const String pattern10 = 'assets/svg/pattern/Asset_10.svg';
  
  static const List<String> allPatterns = [
    pattern4, pattern5, pattern6, pattern7, pattern8, pattern9, pattern10
  ];
}

// ============================================================================
// PATTERN LOADING INDICATOR
// ============================================================================
class PatternLoadingIndicator extends StatefulWidget {
  final double size;
  final Color? color;
  final String? patternAsset;
  
  const PatternLoadingIndicator({
    Key? key,
    this.size = 60,
    this.color,
    this.patternAsset,
  }) : super(key: key);

  @override
  State<PatternLoadingIndicator> createState() => _PatternLoadingIndicatorState();
}

class _PatternLoadingIndicatorState extends State<PatternLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();
    
    _scaleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final pattern = widget.patternAsset ?? PatternAssets.pattern7;
    
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationController, _scaleAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationController.value * 2 * math.pi,
            child: Container(
              width: widget.size,
              height: widget.size,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  SvgPicture.asset(
                    pattern,
                    width: widget.size,
                    height: widget.size,
                    colorFilter: ColorFilter.mode(
                      widget.color ?? PatternColors.deepTeal,
                      BlendMode.srcIn,
                    ),
                  ),
                  // Inner glow effect
                  Container(
                    width: widget.size * 0.6,
                    height: widget.size * 0.6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          (widget.color ?? PatternColors.deepTeal).withOpacity(0.3),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// ============================================================================
// PATTERN OVERLAY WIDGET - For creating beautiful overlay effects
// ============================================================================
class PatternOverlay extends StatelessWidget {
  final Widget child;
  final String patternAsset;
  final double opacity;
  final Color? color;
  final BlendMode blendMode;
  final AlignmentGeometry alignment;
  final double patternScale;
  
  const PatternOverlay({
    Key? key,
    required this.child,
    required this.patternAsset,
    this.opacity = 0.1,
    this.color,
    this.blendMode = BlendMode.srcIn,
    this.alignment = Alignment.center,
    this.patternScale = 1.0,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        Positioned.fill(
          child: IgnorePointer(
            child: Opacity(
              opacity: opacity,
              child: Transform.scale(
                scale: patternScale,
                child: Align(
                  alignment: alignment,
                  child: SvgPicture.asset(
                    patternAsset,
                    fit: BoxFit.cover,
                    colorFilter: color != null
                        ? ColorFilter.mode(color!, blendMode)
                        : null,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// ============================================================================
// PATTERN APP BAR
// ============================================================================
class PatternAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final String? patternAsset;
  
  const PatternAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.patternAsset,
  }) : super(key: key);
  
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 20);
  
  @override
  Widget build(BuildContext context) {
    final pattern = patternAsset ?? PatternAssets.pattern6;
    
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? PatternColors.deepTeal,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: PatternOverlay(
        patternAsset: pattern,
        color: Colors.white,
        opacity: 0.15,
        patternScale: 0.5,
        alignment: Alignment.topRight,
        child: SafeArea(
          bottom: false,
          child: Container(
            height: kToolbarHeight + 20,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                leading ?? const BackButton(color: Colors.white),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
                if (actions != null) ...actions!,
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ============================================================================
// PATTERN SPLASH SCREEN
// ============================================================================
class PatternSplashScreen extends StatefulWidget {
  const PatternSplashScreen({Key? key}) : super(key: key);

  @override
  State<PatternSplashScreen> createState() => _PatternSplashScreenState();
}

class _PatternSplashScreenState extends State<PatternSplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _patternController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  
  final List<PatternLayer> _patternLayers = [
    PatternLayer(
      pattern: PatternAssets.pattern4,
      scale: 2.0,
      rotation: 0,
      offset: const Offset(-100, -100),
      color: PatternColors.gold.withOpacity(0.1),
    ),
    PatternLayer(
      pattern: PatternAssets.pattern6,
      scale: 1.5,
      rotation: math.pi / 4,
      offset: const Offset(100, 100),
      color: PatternColors.azure.withOpacity(0.1),
    ),
    PatternLayer(
      pattern: PatternAssets.pattern8,
      scale: 1.8,
      rotation: -math.pi / 6,
      offset: const Offset(-50, 150),
      color: PatternColors.emerald.withOpacity(0.1),
    ),
  ];
  
  @override
  void initState() {
    super.initState();
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _patternController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    )..repeat();
    
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.3, 0.8, curve: Curves.easeIn),
    ));
    
    _slideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: Curves.easeOutCubic,
    ));
    
    _mainController.forward();
  }
  
  @override
  void dispose() {
    _mainController.dispose();
    _patternController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PatternColors.cream,
      body: Stack(
        children: [
          // Animated pattern background layers
          ..._patternLayers.map((layer) => _buildPatternLayer(layer)),
          
          // Center content
          Center(
            child: AnimatedBuilder(
              animation: _mainController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _slideAnimation.value),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Main logo with pattern
                        Transform.scale(
                          scale: _scaleAnimation.value,
                          child: Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: PatternColors.deepTeal.withOpacity(0.3),
                                  blurRadius: 30,
                                  spreadRadius: 10,
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(30),
                              child: SvgPicture.asset(
                                PatternAssets.pattern7,
                                colorFilter: const ColorFilter.mode(
                                  PatternColors.deepTeal,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 40),
                        // App name
                        Text(
                          'Quranic Insights',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: PatternColors.deepTeal,
                            letterSpacing: 1,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Illuminating Divine Wisdom',
                          style: TextStyle(
                            fontSize: 16,
                            color: PatternColors.deepTeal.withOpacity(0.7),
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                        const SizedBox(height: 60),
                        // Loading indicator
                        const PatternLoadingIndicator(
                          size: 40,
                          patternAsset: PatternAssets.pattern9,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPatternLayer(PatternLayer layer) {
    return AnimatedBuilder(
      animation: _patternController,
      builder: (context, child) {
        final rotation = layer.rotation + (_patternController.value * 2 * math.pi * 0.1);
        return Positioned(
          left: layer.offset.dx,
          top: layer.offset.dy,
          child: Transform.rotate(
            angle: rotation,
            child: Transform.scale(
              scale: layer.scale,
              child: Opacity(
                opacity: 0.1,
                child: SvgPicture.asset(
                  layer.pattern,
                  width: 200,
                  height: 200,
                  colorFilter: ColorFilter.mode(
                    layer.color,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Helper class for pattern layers
class PatternLayer {
  final String pattern;
  final double scale;
  final double rotation;
  final Offset offset;
  final Color color;
  
  PatternLayer({
    required this.pattern,
    required this.scale,
    required this.rotation,
    required this.offset,
    required this.color,
  });
}

// ============================================================================
// PATTERN CARD with multiple pattern decorations
// ============================================================================
class PatternCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final double? elevation;
  final List<String>? patterns; // Use multiple patterns
  
  const PatternCard({
    Key? key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.onTap,
    this.elevation,
    this.patterns,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final cardPatterns = patterns ?? [PatternAssets.pattern5, PatternAssets.pattern9];
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: PatternColors.deepTeal.withOpacity(0.1),
                blurRadius: elevation ?? 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Multiple corner pattern decorations
              if (cardPatterns.isNotEmpty)
                Positioned(
                  top: -30,
                  right: -30,
                  child: Opacity(
                    opacity: 0.05,
                    child: Transform.rotate(
                      angle: math.pi / 6,
                      child: SvgPicture.asset(
                        cardPatterns[0],
                        width: 80,
                        height: 80,
                        colorFilter: const ColorFilter.mode(
                          PatternColors.deepTeal,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
              if (cardPatterns.length > 1)
                Positioned(
                  bottom: -30,
                  left: -30,
                  child: Opacity(
                    opacity: 0.05,
                    child: Transform.rotate(
                      angle: -math.pi / 4,
                      child: SvgPicture.asset(
                        cardPatterns[1],
                        width: 80,
                        height: 80,
                        colorFilter: const ColorFilter.mode(
                          PatternColors.gold,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
              // Content
              Padding(
                padding: padding ?? const EdgeInsets.all(16),
                child: child,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ============================================================================
// PATTERN FLOATING ACTION BUTTON with animated patterns
// ============================================================================
class PatternFloatingActionButton extends StatefulWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final Color? backgroundColor;
  final String? patternAsset;
  
  const PatternFloatingActionButton({
    Key? key,
    required this.onPressed,
    required this.icon,
    this.backgroundColor,
    this.patternAsset,
  }) : super(key: key);

  @override
  State<PatternFloatingActionButton> createState() => _PatternFloatingActionButtonState();
}

class _PatternFloatingActionButtonState extends State<PatternFloatingActionButton>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    )..repeat();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  void dispose() {
    _controller.dispose();
    _pulseController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final pattern = widget.patternAsset ?? PatternAssets.pattern10;
    
    return AnimatedBuilder(
      animation: Listenable.merge([_controller, _pulseAnimation]),
      builder: (context, child) {
        return Container(
          width: 64,
          height: 64,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Animated pattern background
              Transform.scale(
                scale: _pulseAnimation.value,
                child: Transform.rotate(
                  angle: _controller.value * 2 * math.pi,
                  child: Opacity(
                    opacity: 0.2,
                    child: SvgPicture.asset(
                      pattern,
                      width: 64,
                      height: 64,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ),
              // Shadow effect
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: (widget.backgroundColor ?? PatternColors.deepTeal).withOpacity(0.4),
                      blurRadius: 15 + (5 * _pulseAnimation.value),
                      spreadRadius: 2,
                    ),
                  ],
                ),
              ),
              // Button
              FloatingActionButton(
                onPressed: widget.onPressed,
                backgroundColor: widget.backgroundColor ?? PatternColors.deepTeal,
                child: Icon(
                  widget.icon,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// ============================================================================
// PATTERN DIVIDER with rotating patterns
// ============================================================================
class PatternDivider extends StatelessWidget {
  final double? height;
  final Color? color;
  final String? patternAsset;
  
  const PatternDivider({
    Key? key,
    this.height,
    this.color,
    this.patternAsset,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final pattern = patternAsset ?? PatternAssets.pattern4;
    
    return Container(
      height: height ?? 40,
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    (color ?? PatternColors.deepTeal).withOpacity(0.2),
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SvgPicture.asset(
              pattern,
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                (color ?? PatternColors.deepTeal).withOpacity(0.4),
                BlendMode.srcIn,
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    (color ?? PatternColors.deepTeal).withOpacity(0.2),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// PATTERN ICON BUTTON - Decorative icon buttons with pattern backgrounds
// ============================================================================
class PatternIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color? iconColor;
  final Color? backgroundColor;
  final String? patternAsset;
  final double size;
  
  const PatternIconButton({
    Key? key,
    required this.icon,
    required this.onPressed,
    this.iconColor,
    this.backgroundColor,
    this.patternAsset,
    this.size = 48,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final pattern = patternAsset ?? PatternAssets.pattern8;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(size / 2),
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: backgroundColor ?? PatternColors.deepTeal.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Pattern background
              Opacity(
                opacity: 0.2,
                child: SvgPicture.asset(
                  pattern,
                  width: size * 0.8,
                  height: size * 0.8,
                  colorFilter: ColorFilter.mode(
                    iconColor ?? PatternColors.deepTeal,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              // Icon
              Icon(
                icon,
                color: iconColor ?? PatternColors.deepTeal,
                size: size * 0.5,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ============================================================================
// PATTERN THEME DATA
// ============================================================================
class PatternTheme {
  static ThemeData get lightTheme => ThemeData(
    primaryColor: PatternColors.deepTeal,
    scaffoldBackgroundColor: PatternColors.cream,
    fontFamily: 'Roboto',
    colorScheme: const ColorScheme.light(
      primary: PatternColors.deepTeal,
      secondary: PatternColors.gold,
      surface: Colors.white,
      background: PatternColors.cream,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: PatternColors.deepTeal,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: PatternColors.deepTeal,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    ),
  );
  
  static ThemeData get darkTheme => ThemeData(
    primaryColor: PatternColors.midnight,
    scaffoldBackgroundColor: PatternColors.midnight,
    fontFamily: 'Roboto',
    colorScheme: ColorScheme.dark(
      primary: PatternColors.gold,
      secondary: PatternColors.deepTeal,
      surface: PatternColors.midnight.withOpacity(0.8),
      background: PatternColors.midnight,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: PatternColors.midnight,
      foregroundColor: PatternColors.gold,
      elevation: 0,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: PatternColors.gold,
        foregroundColor: PatternColors.midnight,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      color: PatternColors.midnight.withOpacity(0.8),
    ),
  );
}