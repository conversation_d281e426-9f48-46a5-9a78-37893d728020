import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'pattern_identity.dart';

/// Pattern-based Surah Banner variations
/// Multiple designs using the pattern SVGs for beautiful Surah headers

// ============================================================================
// GEOMETRIC PATTERN BANNER - Layered patterns with gradient
// ============================================================================
class GeometricPatternBanner extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  final Color? primaryColor;
  final Color? secondaryColor;
  
  const GeometricPatternBanner({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
    this.primaryColor,
    this.secondaryColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final primary = primaryColor ?? PatternColors.deepTeal;
    final secondary = secondaryColor ?? PatternColors.gold;
    
    return Container(
      height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primary,
                  primary.withOpacity(0.8),
                  secondary.withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: primary.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
          ),
          
          // Pattern overlay - left side
          Positioned(
            left: -50,
            top: -50,
            child: Transform.rotate(
              angle: math.pi / 6,
              child: Opacity(
                opacity: 0.1,
                child: SvgPicture.asset(
                  PatternAssets.pattern6,
                  width: 200,
                  height: 200,
                  colorFilter: const ColorFilter.mode(
                    Colors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          
          // Pattern overlay - right side
          Positioned(
            right: -30,
            bottom: -30,
            child: Transform.rotate(
              angle: -math.pi / 8,
              child: Opacity(
                opacity: 0.15,
                child: SvgPicture.asset(
                  PatternAssets.pattern8,
                  width: 150,
                  height: 150,
                  colorFilter: const ColorFilter.mode(
                    Colors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          
          // Center content
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Surah number with pattern
                Container(
                  width: 50,
                  height: 50,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      SvgPicture.asset(
                        PatternAssets.pattern10,
                        width: 50,
                        height: 50,
                        colorFilter: ColorFilter.mode(
                          Colors.white.withOpacity(0.3),
                          BlendMode.srcIn,
                        ),
                      ),
                      Text(
                        surahNumber.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                
                // Arabic name
                Text(
                  surahNameArabic,
                  style: const TextStyle(
                    fontFamily: 'uthmanic2',
                    fontSize: 32,
                    color: Colors.white,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 8),
                
                // English name
                Text(
                  surahName,
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w300,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Divider with pattern
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 1,
                        color: Colors.white.withOpacity(0.3),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: SvgPicture.asset(
                        PatternAssets.pattern5,
                        width: 20,
                        height: 20,
                        colorFilter: ColorFilter.mode(
                          Colors.white.withOpacity(0.5),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        height: 1,
                        color: Colors.white.withOpacity(0.3),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Metadata
                Text(
                  '$revelationType • $ayahCount Ayahs',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// MINIMALIST PATTERN BANNER - Clean with subtle pattern accents
// ============================================================================
class MinimalistPatternBanner extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  final Color? accentColor;
  
  const MinimalistPatternBanner({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
    this.accentColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final accent = accentColor ?? PatternColors.deepTeal;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Top border with pattern
          Container(
            height: 4,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  accent.withOpacity(0.6),
                  accent,
                  accent.withOpacity(0.6),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
          ),
          
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                // Left pattern accent
                Container(
                  width: 80,
                  height: 80,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      SvgPicture.asset(
                        PatternAssets.pattern7,
                        width: 80,
                        height: 80,
                        colorFilter: ColorFilter.mode(
                          accent.withOpacity(0.1),
                          BlendMode.srcIn,
                        ),
                      ),
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: accent.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            surahNumber.toString(),
                            style: TextStyle(
                              color: accent,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                
                // Center content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        surahNameArabic,
                        style: TextStyle(
                          fontFamily: 'uthmanic2',
                          fontSize: 28,
                          color: accent,
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        surahName,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildMetadataChip(revelationType, accent),
                          const SizedBox(width: 8),
                          _buildMetadataChip('$ayahCount Ayahs', accent),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Right pattern decoration
                SvgPicture.asset(
                  PatternAssets.pattern4,
                  width: 40,
                  height: 40,
                  colorFilter: ColorFilter.mode(
                    accent.withOpacity(0.2),
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildMetadataChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

// ============================================================================
// ORNATE PATTERN BANNER - Rich with multiple layered patterns
// ============================================================================
class OrnatePatternBanner extends StatefulWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  
  const OrnatePatternBanner({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
  }) : super(key: key);

  @override
  State<OrnatePatternBanner> createState() => _OrnatePatternBannerState();
}

class _OrnatePatternBannerState extends State<OrnatePatternBanner>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 250,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        children: [
          // Base container with gradient
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  PatternColors.midnight,
                  Color(0xFF2A3F5F),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: PatternColors.midnight.withOpacity(0.5),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
          ),
          
          // Animated pattern layers
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Stack(
                children: [
                  // Layer 1 - Large background pattern
                  Positioned(
                    top: -100,
                    left: -100,
                    child: Transform.rotate(
                      angle: _controller.value * 0.5 * math.pi,
                      child: Opacity(
                        opacity: 0.05,
                        child: SvgPicture.asset(
                          PatternAssets.pattern6,
                          width: 400,
                          height: 400,
                          colorFilter: const ColorFilter.mode(
                            PatternColors.gold,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // Layer 2 - Medium pattern right
                  Positioned(
                    right: -50,
                    top: 50,
                    child: Transform.rotate(
                      angle: -_controller.value * 0.3 * math.pi,
                      child: Opacity(
                        opacity: 0.1,
                        child: SvgPicture.asset(
                          PatternAssets.pattern9,
                          width: 200,
                          height: 200,
                          colorFilter: const ColorFilter.mode(
                            Colors.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          
          // Content
          Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Ornate frame with surah number
                Container(
                  width: 100,
                  height: 100,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer pattern
                      SvgPicture.asset(
                        PatternAssets.pattern10,
                        width: 100,
                        height: 100,
                        colorFilter: const ColorFilter.mode(
                          PatternColors.gold,
                          BlendMode.srcIn,
                        ),
                      ),
                      // Inner pattern
                      SvgPicture.asset(
                        PatternAssets.pattern7,
                        width: 60,
                        height: 60,
                        colorFilter: ColorFilter.mode(
                          PatternColors.gold.withOpacity(0.3),
                          BlendMode.srcIn,
                        ),
                      ),
                      // Number
                      Text(
                        widget.surahNumber.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                
                // Arabic name with glow
                Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: PatternColors.gold.withOpacity(0.3),
                        blurRadius: 20,
                      ),
                    ],
                  ),
                  child: Text(
                    widget.surahNameArabic,
                    style: const TextStyle(
                      fontFamily: 'uthmanic2',
                      fontSize: 40,
                      color: PatternColors.gold,
                      height: 1.2,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                
                // English name
                Text(
                  widget.surahName,
                  style: const TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                    fontWeight: FontWeight.w300,
                    letterSpacing: 2,
                  ),
                ),
                const SizedBox(height: 20),
                
                // Ornate divider
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      PatternAssets.pattern5,
                      width: 30,
                      height: 30,
                      colorFilter: ColorFilter.mode(
                        PatternColors.gold.withOpacity(0.5),
                        BlendMode.srcIn,
                      ),
                    ),
                    Container(
                      width: 80,
                      height: 1,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      color: PatternColors.gold.withOpacity(0.3),
                    ),
                    SvgPicture.asset(
                      PatternAssets.pattern5,
                      width: 30,
                      height: 30,
                      colorFilter: ColorFilter.mode(
                        PatternColors.gold.withOpacity(0.5),
                        BlendMode.srcIn,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Metadata
                Text(
                  '${widget.revelationType} • ${widget.ayahCount} Ayahs',
                  style: TextStyle(
                    fontSize: 14,
                    color: PatternColors.gold.withOpacity(0.8),
                    letterSpacing: 1,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// ISLAMIC TILE BANNER - Inspired by mosque tile patterns
// ============================================================================
class IslamicTileBanner extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  
  const IslamicTileBanner({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        children: [
          // Tiled pattern background
          Container(
            height: 180,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Stack(
                children: [
                  // Base color
                  Container(
                    color: PatternColors.deepTeal,
                  ),
                  
                  // Tiled pattern grid
                  ...List.generate(12, (index) {
                    final row = index ~/ 4;
                    final col = index % 4;
                    final patterns = [
                      PatternAssets.pattern4,
                      PatternAssets.pattern5,
                      PatternAssets.pattern8,
                      PatternAssets.pattern9,
                    ];
                    
                    return Positioned(
                      left: col * 100.0 - 20,
                      top: row * 60.0 - 10,
                      child: Transform.rotate(
                        angle: (row + col).isEven ? 0 : math.pi / 2,
                        child: Opacity(
                          opacity: 0.1,
                          child: SvgPicture.asset(
                            patterns[index % patterns.length],
                            width: 80,
                            height: 80,
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                  
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          PatternColors.deepTeal.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Content overlay
          Container(
            height: 180,
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Left ornament
                    SvgPicture.asset(
                      PatternAssets.pattern10,
                      width: 40,
                      height: 40,
                      colorFilter: ColorFilter.mode(
                        PatternColors.gold.withOpacity(0.6),
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // Center content
                    Column(
                      children: [
                        Text(
                          surahNumber.toString(),
                          style: const TextStyle(
                            color: PatternColors.gold,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          surahNameArabic,
                          style: const TextStyle(
                            fontFamily: 'uthmanic2',
                            fontSize: 32,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          surahName,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 16),
                    
                    // Right ornament
                    Transform.scale(
                      scaleX: -1,
                      child: SvgPicture.asset(
                        PatternAssets.pattern10,
                        width: 40,
                        height: 40,
                        colorFilter: ColorFilter.mode(
                          PatternColors.gold.withOpacity(0.6),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Metadata bar
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '$revelationType • $ayahCount Ayahs',
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// MODERN GLASS BANNER - Glassmorphic with pattern accents
// ============================================================================
class ModernGlassBanner extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  
  const ModernGlassBanner({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        children: [
          // Background patterns
          Positioned(
            right: -50,
            top: -50,
            child: Opacity(
              opacity: 0.1,
              child: SvgPicture.asset(
                PatternAssets.pattern6,
                width: 250,
                height: 250,
                colorFilter: const ColorFilter.mode(
                  PatternColors.azure,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Glass container
          ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1.5,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Row(
                    children: [
                      // Left section with pattern
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              PatternColors.azure.withOpacity(0.3),
                              PatternColors.deepTeal.withOpacity(0.3),
                            ],
                          ),
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SvgPicture.asset(
                              PatternAssets.pattern7,
                              width: 80,
                              height: 80,
                              colorFilter: ColorFilter.mode(
                                Colors.white.withOpacity(0.3),
                                BlendMode.srcIn,
                              ),
                            ),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Surah',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.8),
                                    fontSize: 12,
                                  ),
                                ),
                                Text(
                                  surahNumber.toString(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 24),
                      
                      // Right section with text
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              surahNameArabic,
                              style: const TextStyle(
                                fontFamily: 'uthmanic2',
                                fontSize: 32,
                                color: PatternColors.midnight,
                                height: 1.2,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              surahName,
                              style: TextStyle(
                                fontSize: 18,
                                color: PatternColors.midnight.withOpacity(0.8),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on_outlined,
                                  size: 16,
                                  color: PatternColors.midnight.withOpacity(0.6),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  revelationType,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: PatternColors.midnight.withOpacity(0.6),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Icon(
                                  Icons.format_list_numbered,
                                  size: 16,
                                  color: PatternColors.midnight.withOpacity(0.6),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '$ayahCount Ayahs',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: PatternColors.midnight.withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}