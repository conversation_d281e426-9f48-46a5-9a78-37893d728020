import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'pattern_identity.dart';

/// Pattern-based Surah banner frames that mimic the style of existing banner SVGs
/// These create ornamental frames similar to surah_banner_custom.svg, minimal.svg, modern.svg

// ============================================================================
// CUSTOM PATTERN BANNER - Ornate frame style like surah_banner_custom.svg
// ============================================================================
class CustomPatternBanner extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  final Color? primaryColor;
  final Color? accentColor;
  
  const CustomPatternBanner({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
    this.primaryColor,
    this.accentColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final primary = primaryColor ?? PatternColors.deepTeal;
    final accent = accentColor ?? PatternColors.gold;
    
    return Container(
      height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Base frame shape
          CustomPaint(
            size: const Size(double.infinity, 200),
            painter: _OrnateFramePainter(
              primaryColor: primary,
              accentColor: accent,
            ),
          ),
          
          // Corner patterns - top left
          Positioned(
            top: 20,
            left: 20,
            child: Transform.rotate(
              angle: -math.pi / 4,
              child: SvgPicture.asset(
                PatternAssets.pattern6,
                width: 60,
                height: 60,
                colorFilter: ColorFilter.mode(
                  accent.withOpacity(0.3),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Corner patterns - top right
          Positioned(
            top: 20,
            right: 20,
            child: Transform.rotate(
              angle: math.pi / 4,
              child: SvgPicture.asset(
                PatternAssets.pattern6,
                width: 60,
                height: 60,
                colorFilter: ColorFilter.mode(
                  accent.withOpacity(0.3),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Corner patterns - bottom left
          Positioned(
            bottom: 20,
            left: 20,
            child: Transform.rotate(
              angle: -3 * math.pi / 4,
              child: SvgPicture.asset(
                PatternAssets.pattern6,
                width: 60,
                height: 60,
                colorFilter: ColorFilter.mode(
                  accent.withOpacity(0.3),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Corner patterns - bottom right
          Positioned(
            bottom: 20,
            right: 20,
            child: Transform.rotate(
              angle: 3 * math.pi / 4,
              child: SvgPicture.asset(
                PatternAssets.pattern6,
                width: 60,
                height: 60,
                colorFilter: ColorFilter.mode(
                  accent.withOpacity(0.3),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Center content
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Surah number in ornate circle
              Container(
                width: 50,
                height: 50,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SvgPicture.asset(
                      PatternAssets.pattern10,
                      width: 50,
                      height: 50,
                      colorFilter: ColorFilter.mode(
                        primary,
                        BlendMode.srcIn,
                      ),
                    ),
                    Text(
                      surahNumber.toString(),
                      style: TextStyle(
                        color: primary,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              
              // Arabic name
              Text(
                surahNameArabic,
                style: TextStyle(
                  fontFamily: 'uthmanic2',
                  fontSize: 32,
                  color: primary,
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 8),
              
              // English name
              Text(
                surahName,
                style: TextStyle(
                  fontSize: 16,
                  color: primary.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              
              // Divider with pattern
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 40,
                    height: 1,
                    color: accent.withOpacity(0.5),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: SvgPicture.asset(
                      PatternAssets.pattern5,
                      width: 16,
                      height: 16,
                      colorFilter: ColorFilter.mode(
                        accent,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 1,
                    color: accent.withOpacity(0.5),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              
              // Metadata
              Text(
                '$revelationType • $ayahCount Ayahs',
                style: TextStyle(
                  fontSize: 13,
                  color: primary.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// MINIMAL PATTERN BANNER - Clean style like surah_banner_minimal.svg
// ============================================================================
class MinimalPatternBannerFrame extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  final Color? primaryColor;
  
  const MinimalPatternBannerFrame({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
    this.primaryColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final primary = primaryColor ?? PatternColors.deepTeal;
    
    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        children: [
          // Simple frame
          CustomPaint(
            size: const Size(double.infinity, 180),
            painter: _MinimalFramePainter(color: primary),
          ),
          
          // Single centered pattern behind content
          Center(
            child: Opacity(
              opacity: 0.05,
              child: SvgPicture.asset(
                PatternAssets.pattern7,
                width: 140,
                height: 140,
                colorFilter: ColorFilter.mode(
                  primary,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Content
          Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Simple number
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: primary,
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      surahNumber.toString(),
                      style: TextStyle(
                        color: primary,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Names
                Text(
                  surahNameArabic,
                  style: TextStyle(
                    fontFamily: 'uthmanic2',
                    fontSize: 28,
                    color: primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  surahName,
                  style: TextStyle(
                    fontSize: 14,
                    color: primary.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 12),
                
                // Simple divider
                Container(
                  width: 60,
                  height: 1,
                  color: primary.withOpacity(0.3),
                ),
                const SizedBox(height: 12),
                
                // Metadata
                Text(
                  '$revelationType • $ayahCount',
                  style: TextStyle(
                    fontSize: 12,
                    color: primary.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// MODERN PATTERN BANNER - Contemporary style like surah_banner_modern.svg
// ============================================================================
class ModernPatternBannerFrame extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  final Color? primaryColor;
  final Color? secondaryColor;
  
  const ModernPatternBannerFrame({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
    this.primaryColor,
    this.secondaryColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final primary = primaryColor ?? PatternColors.deepTeal;
    final secondary = secondaryColor ?? PatternColors.azure;
    
    return Container(
      height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        children: [
          // Modern geometric frame
          CustomPaint(
            size: const Size(double.infinity, 200),
            painter: _ModernFramePainter(
              primaryColor: primary,
              secondaryColor: secondary,
            ),
          ),
          
          // Asymmetric pattern placement
          Positioned(
            top: -20,
            right: -20,
            child: Transform.rotate(
              angle: math.pi / 6,
              child: Opacity(
                opacity: 0.1,
                child: SvgPicture.asset(
                  PatternAssets.pattern8,
                  width: 100,
                  height: 100,
                  colorFilter: ColorFilter.mode(
                    secondary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          
          Positioned(
            bottom: -30,
            left: 40,
            child: Transform.rotate(
              angle: -math.pi / 8,
              child: Opacity(
                opacity: 0.08,
                child: SvgPicture.asset(
                  PatternAssets.pattern9,
                  width: 120,
                  height: 120,
                  colorFilter: ColorFilter.mode(
                    primary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          
          // Content with modern layout
          Padding(
            padding: const EdgeInsets.all(32),
            child: Row(
              children: [
                // Left side - number
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        primary.withOpacity(0.1),
                        secondary.withOpacity(0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      SvgPicture.asset(
                        PatternAssets.pattern4,
                        width: 60,
                        height: 60,
                        colorFilter: ColorFilter.mode(
                          primary.withOpacity(0.2),
                          BlendMode.srcIn,
                        ),
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Surah',
                            style: TextStyle(
                              fontSize: 10,
                              color: primary.withOpacity(0.7),
                            ),
                          ),
                          Text(
                            surahNumber.toString(),
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: primary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 24),
                
                // Right side - text
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        surahNameArabic,
                        style: TextStyle(
                          fontFamily: 'uthmanic2',
                          fontSize: 30,
                          color: primary,
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        surahName,
                        style: TextStyle(
                          fontSize: 16,
                          color: secondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '$revelationType • $ayahCount Ayahs',
                          style: TextStyle(
                            fontSize: 12,
                            color: primary.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// TRADITIONAL PATTERN BANNER - Classic Islamic frame design
// ============================================================================
class TraditionalPatternBanner extends StatelessWidget {
  final String surahName;
  final String surahNameArabic;
  final int surahNumber;
  final int ayahCount;
  final String revelationType;
  
  const TraditionalPatternBanner({
    Key? key,
    required this.surahName,
    required this.surahNameArabic,
    required this.surahNumber,
    required this.ayahCount,
    required this.revelationType,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 220,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Stack(
        children: [
          // Traditional Islamic frame
          CustomPaint(
            size: const Size(double.infinity, 220),
            painter: _TraditionalFramePainter(),
          ),
          
          // Symmetrical pattern arrangement
          // Top center
          Positioned(
            top: 10,
            left: 0,
            right: 0,
            child: Center(
              child: SvgPicture.asset(
                PatternAssets.pattern10,
                width: 50,
                height: 50,
                colorFilter: ColorFilter.mode(
                  PatternColors.gold.withOpacity(0.5),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Side patterns
          Positioned(
            top: 60,
            left: 20,
            child: SvgPicture.asset(
              PatternAssets.pattern5,
              width: 40,
              height: 40,
              colorFilter: ColorFilter.mode(
                PatternColors.deepTeal.withOpacity(0.3),
                BlendMode.srcIn,
              ),
            ),
          ),
          Positioned(
            top: 60,
            right: 20,
            child: Transform.scale(
              scaleX: -1,
              child: SvgPicture.asset(
                PatternAssets.pattern5,
                width: 40,
                height: 40,
                colorFilter: ColorFilter.mode(
                  PatternColors.deepTeal.withOpacity(0.3),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Bottom center
          Positioned(
            bottom: 10,
            left: 0,
            right: 0,
            child: Center(
              child: Transform.rotate(
                angle: math.pi,
                child: SvgPicture.asset(
                  PatternAssets.pattern10,
                  width: 50,
                  height: 50,
                  colorFilter: ColorFilter.mode(
                    PatternColors.gold.withOpacity(0.5),
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          
          // Content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Surah number
                Text(
                  'سورة',
                  style: TextStyle(
                    fontFamily: 'uthmanic2',
                    fontSize: 14,
                    color: PatternColors.deepTeal.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: PatternColors.gold,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    surahNumber.toString(),
                    style: const TextStyle(
                      color: PatternColors.gold,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Arabic name
                Text(
                  surahNameArabic,
                  style: const TextStyle(
                    fontFamily: 'uthmanic2',
                    fontSize: 36,
                    color: PatternColors.deepTeal,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 8),
                
                // English name
                Text(
                  surahName,
                  style: TextStyle(
                    fontSize: 16,
                    color: PatternColors.deepTeal.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Traditional divider
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildOrnament(),
                    Container(
                      width: 60,
                      height: 2,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            PatternColors.gold.withOpacity(0),
                            PatternColors.gold,
                            PatternColors.gold.withOpacity(0),
                          ],
                        ),
                      ),
                    ),
                    _buildOrnament(),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Metadata
                Text(
                  '$revelationType • $ayahCount Ayahs',
                  style: TextStyle(
                    fontSize: 13,
                    color: PatternColors.deepTeal.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildOrnament() {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: PatternColors.gold,
      ),
    );
  }
}

// Custom Painters for different frame styles

class _OrnateFramePainter extends CustomPainter {
  final Color primaryColor;
  final Color accentColor;
  
  _OrnateFramePainter({
    required this.primaryColor,
    required this.accentColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..color = primaryColor.withOpacity(0.3);
    
    final rect = Rect.fromLTWH(20, 10, size.width - 40, size.height - 20);
    
    // Outer frame with curves
    final path = Path();
    path.moveTo(rect.left + 40, rect.top);
    path.lineTo(rect.right - 40, rect.top);
    path.quadraticBezierTo(rect.right, rect.top, rect.right, rect.top + 40);
    path.lineTo(rect.right, rect.bottom - 40);
    path.quadraticBezierTo(rect.right, rect.bottom, rect.right - 40, rect.bottom);
    path.lineTo(rect.left + 40, rect.bottom);
    path.quadraticBezierTo(rect.left, rect.bottom, rect.left, rect.bottom - 40);
    path.lineTo(rect.left, rect.top + 40);
    path.quadraticBezierTo(rect.left, rect.top, rect.left + 40, rect.top);
    
    canvas.drawPath(path, paint);
    
    // Inner decorative frame
    paint.color = accentColor.withOpacity(0.2);
    final innerRect = Rect.fromLTWH(30, 20, size.width - 60, size.height - 40);
    canvas.drawRect(innerRect, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _MinimalFramePainter extends CustomPainter {
  final Color color;
  
  _MinimalFramePainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1
      ..color = color.withOpacity(0.3);
    
    // Simple rectangle with subtle corners
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(10, 10, size.width - 20, size.height - 20),
      const Radius.circular(8),
    );
    
    canvas.drawRRect(rect, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _ModernFramePainter extends CustomPainter {
  final Color primaryColor;
  final Color secondaryColor;
  
  _ModernFramePainter({
    required this.primaryColor,
    required this.secondaryColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Gradient background
    final gradient = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          primaryColor.withOpacity(0.05),
          secondaryColor.withOpacity(0.05),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      const Radius.circular(16),
    );
    
    canvas.drawRRect(rect, gradient);
    
    // Modern border
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..shader = LinearGradient(
        colors: [primaryColor, secondaryColor],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    
    canvas.drawRRect(rect, borderPaint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _TraditionalFramePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = PatternColors.gold.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;
    
    // Outer traditional frame
    final outerRect = Rect.fromLTWH(10, 10, size.width - 20, size.height - 20);
    canvas.drawRect(outerRect, paint);
    
    // Inner frame
    paint.strokeWidth = 1;
    paint.color = PatternColors.deepTeal.withOpacity(0.2);
    final innerRect = Rect.fromLTWH(20, 20, size.width - 40, size.height - 40);
    canvas.drawRect(innerRect, paint);
    
    // Corner decorations
    paint.strokeWidth = 2;
    paint.color = PatternColors.gold.withOpacity(0.5);
    const cornerSize = 20.0;
    
    // Top left
    canvas.drawLine(Offset(10, 10), Offset(10 + cornerSize, 10), paint);
    canvas.drawLine(Offset(10, 10), Offset(10, 10 + cornerSize), paint);
    
    // Top right
    canvas.drawLine(Offset(size.width - 10 - cornerSize, 10), Offset(size.width - 10, 10), paint);
    canvas.drawLine(Offset(size.width - 10, 10), Offset(size.width - 10, 10 + cornerSize), paint);
    
    // Bottom left
    canvas.drawLine(Offset(10, size.height - 10), Offset(10 + cornerSize, size.height - 10), paint);
    canvas.drawLine(Offset(10, size.height - 10 - cornerSize), Offset(10, size.height - 10), paint);
    
    // Bottom right
    canvas.drawLine(Offset(size.width - 10 - cornerSize, size.height - 10), Offset(size.width - 10, size.height - 10), paint);
    canvas.drawLine(Offset(size.width - 10, size.height - 10 - cornerSize), Offset(size.width - 10, size.height - 10), paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}